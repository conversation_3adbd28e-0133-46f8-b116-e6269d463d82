#!/bin/bash

# 服务状态检查脚本
# 用于检查远程Spring Boot服务的运行状态

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="${SCRIPT_DIR}/deploy.config"

# 默认配置变量
REMOTE_USER="your_username"
REMOTE_HOST="your_server_ip"
REMOTE_PORT=22
REMOTE_PATH="/home/<USER>"
JAR_NAME="ContentManager-0.0.4-SNAPSHOT.jar"
SERVER_PORT=8080

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# 加载配置文件
load_config() {
    if [[ -f "$CONFIG_FILE" ]]; then
        log_info "加载配置文件: $CONFIG_FILE"
        while IFS='=' read -r key value; do
            [[ $key =~ ^#.*$ ]] && continue
            [[ -z $key ]] && continue
            value=$(echo "$value" | sed 's/^"\|"$//g')
            eval "$key='$value'"
        done < "$CONFIG_FILE"
    else
        log_warn "配置文件未找到: $CONFIG_FILE"
        log_warn "将使用默认配置或命令行参数"
    fi
}

# SSH连接辅助函数
get_ssh_cmd() {
    echo "ssh -o StrictHostKeyChecking=no -p $REMOTE_PORT"
}

# 检查服务状态
check_service_status() {
    log_header "检查远程服务状态"
    
    local SSH_CMD=$(get_ssh_cmd)
    
    $SSH_CMD ${REMOTE_USER}@${REMOTE_HOST} "
        cd ${REMOTE_PATH} 2>/dev/null || { echo '✗ 部署目录不存在: ${REMOTE_PATH}'; exit 1; }
        
        echo '1. Java进程检查:'
        JAVA_PROCESS=\$(ps aux | grep 'java.*${JAR_NAME}' | grep -v grep)
        if [[ -n \"\$JAVA_PROCESS\" ]]; then
            echo '✓ Java进程运行中:'
            echo \"  \$JAVA_PROCESS\"
            JAVA_PID=\$(echo \"\$JAVA_PROCESS\" | awk '{print \$2}')
            echo \"  进程启动时间: \$(ps -o lstart= -p \$JAVA_PID 2>/dev/null || echo '未知')\"
        else
            echo '✗ Java进程未运行'
        fi
        
        echo ''
        echo '2. 端口监听检查:'
        if netstat -tlnp 2>/dev/null | grep \":${SERVER_PORT} \" > /dev/null; then
            echo \"✓ 端口 ${SERVER_PORT} 正在监听\"
            netstat -tlnp 2>/dev/null | grep \":${SERVER_PORT} \"
        elif ss -tlnp 2>/dev/null | grep \":${SERVER_PORT} \" > /dev/null; then
            echo \"✓ 端口 ${SERVER_PORT} 正在监听\"
            ss -tlnp 2>/dev/null | grep \":${SERVER_PORT} \"
        else
            echo \"✗ 端口 ${SERVER_PORT} 未监听\"
        fi
        
        echo ''
        echo '3. 应用文件检查:'
        if [[ -f ${JAR_NAME} ]]; then
            echo \"✓ JAR文件存在: ${JAR_NAME}\"
            echo \"  文件大小: \$(du -h ${JAR_NAME} | cut -f1)\"
            echo \"  修改时间: \$(stat -c '%y' ${JAR_NAME} 2>/dev/null || stat -f '%Sm' ${JAR_NAME} 2>/dev/null)\"
        else
            echo \"✗ JAR文件不存在: ${JAR_NAME}\"
        fi
        
        echo ''
        echo '4. 日志文件检查:'
        if [[ -f logs/app.log ]]; then
            echo '✓ 日志文件存在'
            echo \"  文件大小: \$(du -h logs/app.log | cut -f1)\"
            echo \"  最后修改: \$(stat -c '%y' logs/app.log 2>/dev/null || stat -f '%Sm' logs/app.log 2>/dev/null)\"
            echo ''
            echo '最新日志内容 (最后20行):'
            echo '----------------------------------------'
            tail -20 logs/app.log
            echo '----------------------------------------'
        else
            echo '✗ 日志文件不存在: logs/app.log'
        fi
        
        echo ''
        echo '5. 系统资源使用:'
        echo '内存使用情况:'
        free -h 2>/dev/null || echo '无法获取内存信息'
        echo ''
        echo '磁盘使用情况:'
        df -h . 2>/dev/null || echo '无法获取磁盘信息'
        
        echo ''
        echo '6. 网络连接检查:'
        if command -v curl >/dev/null 2>&1; then
            echo '测试HTTP连接:'
            if curl -s --connect-timeout 5 http://localhost:${SERVER_PORT} >/dev/null; then
                echo \"✓ HTTP服务响应正常 (端口 ${SERVER_PORT})\"
            else
                echo \"✗ HTTP服务无响应 (端口 ${SERVER_PORT})\"
            fi
        else
            echo '未安装curl，跳过HTTP连接测试'
        fi
    "
    
    local exit_code=$?
    
    echo ""
    if [[ $exit_code -eq 0 ]]; then
        log_info "服务状态检查完成"
    else
        log_error "服务状态检查失败"
    fi
    
    return $exit_code
}

# 脚本使用说明
usage() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -h, --help       显示帮助信息"
    echo "  -u, --user       指定远程用户名"
    echo "  -s, --server     指定远程服务器IP"
    echo ""
    echo "示例:"
    echo "  $0                           # 使用deploy.config配置文件"
    echo "  $0 -u root -s *************  # 使用命令行参数"
}

# 命令行参数解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            usage
            exit 0
            ;;
        -u|--user)
            REMOTE_USER="$2"
            shift 2
            ;;
        -s|--server)
            REMOTE_HOST="$2"
            shift 2
            ;;
        --user=*)
            REMOTE_USER="${1#*=}"
            shift
            ;;
        --server=*)
            REMOTE_HOST="${1#*=}"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            usage
            exit 1
            ;;
    esac
done

# 主函数
main() {
    load_config
    
    log_info "检查目标服务器: ${REMOTE_USER}@${REMOTE_HOST}"
    log_info "服务端口: ${SERVER_PORT}"
    log_info "部署路径: ${REMOTE_PATH}"
    
    # 检查配置
    if [[ "$REMOTE_USER" == "your_username" ]] || [[ "$REMOTE_HOST" == "your_server_ip" ]]; then
        log_error "请先配置 deploy.config 文件或使用命令行参数"
        usage
        exit 1
    fi
    
    # 测试SSH连接
    log_info "测试SSH连接..."
    if ! ssh -o BatchMode=yes -o ConnectTimeout=5 -p $REMOTE_PORT ${REMOTE_USER}@${REMOTE_HOST} exit 2>/dev/null; then
        log_error "SSH连接失败，请检查网络和认证配置"
        exit 1
    fi
    log_info "SSH连接正常"
    
    # 检查服务状态
    check_service_status
}

# 执行主函数
main
